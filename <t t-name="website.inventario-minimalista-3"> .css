<t t-name="website.inventario-minimalista-3">
  <t t-call="website.layout">
    <t t-set="head">
      <title>SeminuevosMex | Chat Sofi Integrado | Sistema BigData</title>
      <meta name="description" content="Sistema de inventario con chat Sofi completamente integrado"/>
      <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
      
      <script src="//code.tidio.co/tm9lkvtrg1t8v0ia5z8gw9vsgzlexprw.js" async=""/>
      
      <!-- CSS MANTENIDO (MISMO QUE FUNCIONA) -->
      <style>
        *{box-sizing:border-box}body{margin:0;padding:0}.dark-inventory{background:#0a0a0a;color:#ffffff;min-height:100vh;font-family:-apple-system,BlinkMacSystemFont,sans-serif;line-height:1.4}.dark-header{text-align:center;padding:25px 15px;background:linear-gradient(135deg,#1a1a2e,#16213e);margin-bottom:20px;border-bottom:1px solid #374151}.dark-header h1{font-size:1.8rem;font-weight:800;background:linear-gradient(135deg,#a855f7,#7c3aed);-webkit-background-clip:text;-webkit-text-fill-color:transparent;margin:0 0 8px 0}.dark-header p{color:#9ca3af;font-size:0.85rem;margin:0}.main-controls{max-width:1200px;margin:0 auto;padding:0 15px 20px 15px}.search-container{margin-bottom:15px}.search-input{width:100%;padding:14px 16px;background:#1f1f1f;border:1px solid #374151;border-radius:8px;color:#ffffff;font-size:0.9rem;transition:border-color 0.2s ease}.search-input:focus{outline:none;border-color:#7c3aed;box-shadow:0 0 0 2px rgba(124,58,237,0.1)}.control-row{display:flex;gap:12px;margin-bottom:15px;align-items:center;flex-wrap:wrap}.sort-section{display:flex;gap:8px;align-items:center;flex:1}.sort-label{font-size:0.8rem;color:#9ca3af;white-space:nowrap;margin-right:8px}.sort-btn{padding:10px 16px;background:#1f1f1f;border:1px solid #374151;border-radius:6px;color:#ffffff;font-size:0.8rem;cursor:pointer;transition:all 0.2s ease;user-select:none;font-weight:500}.sort-btn:hover{border-color:#7c3aed;background:#2d2d2d}.sort-btn.active{background:#7c3aed;border-color:#7c3aed;color:#ffffff}.clear-btn{padding:10px 16px;background:#374151;border:1px solid #4b5563;border-radius:6px;color:#cbd5e1;font-size:0.8rem;cursor:pointer;transition:all 0.2s ease;white-space:nowrap}.clear-btn:hover{background:#4b5563;border-color:#6b7280}.clear-btn.has-filters{background:#ef4444;border-color:#dc2626;color:#ffffff}.filter-tags-container{min-height:28px;margin-bottom:12px}.filter-tags{display:flex;flex-wrap:wrap;gap:6px}.filter-tag{background:#7c3aed;color:#ffffff;padding:4px 10px;border-radius:4px;font-size:0.7rem;display:flex;align-items:center;gap:6px}.tag-remove{cursor:pointer;font-weight:bold;font-size:0.9rem;line-height:1}.tag-remove:hover{color:#fbbf24}.results-counter{text-align:right;font-size:0.8rem;color:#9ca3af;margin-bottom:12px}.table-container{max-width:1200px;margin:0 auto;padding:0 15px}.inventory-table{width:100%;background:#1a1a1a;border:1px solid #374151;border-radius:8px;overflow:hidden;border-collapse:separate;border-spacing:0}.table-header{background:#111111}.table-header th{padding:12px 8px;text-align:center;font-weight:700;font-size:0.75rem;color:#ffffff;border-right:1px solid #374151;position:relative;cursor:pointer;transition:background-color 0.2s ease;user-select:none}.table-header th:last-child{border-right:none}.table-header th:hover{background:#2d2d2d}.table-header th.has-active-filter{background:#7c3aed}.filter-header{display:flex;align-items:center;justify-content:center;gap:4px;padding:4px}.filter-arrow{font-size:0.7rem;transition:transform 0.2s ease;color:#9ca3af}.filter-arrow.rotated{transform:rotate(180deg)}.filter-dropdown{position:absolute;top:100%;left:-1px;right:-1px;background:#1f1f1f;border:2px solid #7c3aed;border-top:none;z-index:1000;border-radius:0 0 8px 8px;box-shadow:0 8px 25px rgba(0,0,0,0.5);max-height:0;overflow:hidden;opacity:0;transform:translateY(-10px);transition:all 0.25s ease}.filter-dropdown.show{max-height:250px;opacity:1;transform:translateY(0)}.filter-options{max-height:200px;overflow-y:auto}.filter-option{padding:12px 16px;font-size:0.75rem;cursor:pointer;border-bottom:1px solid #374151;color:#cbd5e1;transition:all 0.2s ease;display:flex;align-items:center;justify-content:space-between}.filter-option:last-child{border-bottom:none}.filter-option:hover{background:#374151;color:#ffffff}.filter-option.selected{background:#7c3aed;color:#ffffff;font-weight:600}.filter-option.selected::after{content:'✓';font-weight:bold}.table-body{background:#1a1a1a}.table-body tr{border-bottom:1px solid #2d2d2d;transition:background-color 0.15s ease}.table-body tr:hover{background:#1f1f1f}.table-body tr.hidden{display:none}.table-body td{padding:12px 8px;text-align:center;font-size:0.8rem;color:#e5e7eb;border-right:1px solid #2d2d2d}.table-body td:last-child{border-right:none}.vehicle-cell{text-align:left}.vehicle-name{font-weight:700;color:#ffffff;margin-bottom:3px;font-size:0.9rem}.vehicle-details{font-size:0.7rem;color:#9ca3af}.price-cell{text-align:center}.price-old{font-size:0.7rem;color:#ef4444;text-decoration:line-through;display:block;margin-bottom:2px}.price-current{font-size:0.85rem;font-weight:700;color:#10b981;display:block}.details-cell{text-align:center}.spec-tag{display:inline-block;background:#2d2d2d;color:#cbd5e1;padding:2px 6px;border-radius:4px;font-size:0.65rem;margin:1px;border:1px solid #374151}.color-cell{text-align:center}.color-badge{padding:3px 8px;border-radius:6px;font-size:0.65rem;font-weight:600;text-transform:capitalize}.color-azul{background:#3b82f6;color:#ffffff}.color-plata{background:#6b7280;color:#ffffff}.color-rojo{background:#ef4444;color:#ffffff}.color-blanco{background:#f3f4f6;color:#1f2937}.color-gris{background:#4b5563;color:#ffffff}.color-negro{background:#1f2937;color:#ffffff}.color-verde{background:#10b981;color:#ffffff}.color-amarillo{background:#fbbf24;color:#1f2937}.chat-cell{text-align:center}.chat-btn{background:#7c3aed;color:#ffffff;border:none;padding:8px 12px;border-radius:6px;font-size:0.75rem;cursor:pointer;transition:all 0.2s ease;font-weight:600;position:relative}.chat-btn:hover{background:#6d28d9;transform:translateY(-1px);box-shadow:0 4px 12px rgba(124,58,237,0.3)}.chat-btn:active{transform:translateY(0)}.chat-btn::before{content:'';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:0;height:0;border-radius:50%;background:rgba(255,255,255,0.3);transition:all 0.3s ease}.chat-btn:hover::before{width:100%;height:100%}@media (max-width:768px){.dark-header h1{font-size:1.5rem}.control-row{flex-direction:column;align-items:stretch}.sort-section{justify-content:space-between}.filter-dropdown{left:-10px;right:-10px}.table-header th,.table-body td{padding:10px 6px;font-size:0.7rem}.vehicle-name{font-size:0.75rem}.spec-tag{font-size:0.6rem}.chat-btn{padding:6px 10px;font-size:0.7rem}}@media (max-width:480px){.dark-header{padding:20px 10px}.main-controls,.table-container{padding:0 10px}.table-header th,.table-body td{padding:8px 4px;font-size:0.65rem}}
      </style>
      
      <!-- JAVASCRIPT CON CHAT SOFI INTEGRADO Y MEJORADO -->
      <script>
        (function() {
          'use strict';
          
          var InventarioSistema = {
            vehiculos: [],
            vehiculosFiltrados: [],
            filtrosActivos: {},
            menuAbierto: null,
            ordenActual: 'precio',
            debug: true,
            inicializado: false,
            
            log: function(mensaje, tipo) {
              if (this.debug) {
                var prefix = '[INVENTARIO-SOFI]';
                if (tipo === 'error') {
                  console.error(prefix, mensaje);
                } else if (tipo === 'warn') {
                  console.warn(prefix, mensaje);
                } else {
                  console.log(prefix, mensaje);
                }
              }
            },
            
            init: function() {
              if (this.inicializado) {
                this.log('Sistema ya inicializado', 'warn');
                return;
              }
              
              this.log('=== INICIANDO SISTEMA CON CHAT SOFI INTEGRADO ===');
              
              try {
                this.cargarVehiculos();
                this.configurarEventos();
                this.configurarChatSofi();
                this.aplicarFiltros();
                this.renderizar();
                this.actualizarInterfaz();
                this.inicializado = true;
                this.log('=== SISTEMA CON CHAT SOFI INICIALIZADO EXITOSAMENTE ===');
              } catch (error) {
                this.log('Error durante inicialización: ' + error.message, 'error');
              }
            },
            
            cargarVehiculos: function() {
              var selector = '#tabla-inventario tbody tr[data-vehiculo]';
              var filas = document.querySelectorAll(selector);
              this.vehiculos = [];
              
              this.log('Selector usado: ' + selector);
              this.log('Filas encontradas: ' + filas.length);
              
              if (filas.length === 0) {
                this.log('NO SE ENCONTRARON FILAS - Verificar HTML', 'error');
                return;
              }
              
              for (var i = 0; i &lt; filas.length; i++) {
                var fila = filas[i];
                var vehiculo = {
                  elemento: fila,
                  vehiculo: fila.getAttribute('data-vehiculo') || '',
                  marca: fila.getAttribute('data-marca') || '',
                  modelo: fila.getAttribute('data-modelo') || '',
                  año: parseInt(fila.getAttribute('data-año')) || 0,
                  precio: parseInt(fila.getAttribute('data-precio')) || 0,
                  km: parseInt(fila.getAttribute('data-km')) || 0,
                  transmision: fila.getAttribute('data-transmision') || '',
                  combustible: fila.getAttribute('data-combustible') || '',
                  color: fila.getAttribute('data-color') || '',
                  tipo: fila.getAttribute('data-tipo') || '',
                  ubicacion: fila.getAttribute('data-ubicacion') || 'Mexico',
                  variante: fila.getAttribute('data-variante') || ''
                };
                
                this.vehiculos.push(vehiculo);
                this.log('Vehículo ' + (i + 1) + ': ' + vehiculo.vehiculo + ' - $' + vehiculo.precio);
              }
              
              this.vehiculosFiltrados = this.vehiculos.slice();
              this.log('Total vehículos cargados: ' + this.vehiculos.length);
            },
            
            configurarChatSofi: function() {
              this.log('=== CONFIGURANDO CHAT SOFI ===');
              var self = this;
              
              // Configurar botones de chat específicos
              document.addEventListener('click', function(e) {
                if (e.target.classList.contains('chat-btn')) {
                  e.preventDefault();
                  e.stopPropagation();
                  
                  // Obtener datos del vehículo desde la fila
                  var fila = e.target.closest('tr');
                  if (fila) {
                    var vehiculoData = {
                      vehiculo: fila.getAttribute('data-vehiculo') || 'Vehículo',
                      precio: fila.getAttribute('data-precio') || '0',
                      ubicacion: fila.getAttribute('data-ubicacion') || 'Mexico',
                      km: fila.getAttribute('data-km') || '0',
                      año: fila.getAttribute('data-año') || 'N/A',
                      transmision: fila.getAttribute('data-transmision') || 'N/A',
                      combustible: fila.getAttribute('data-combustible') || 'N/A',
                      color: fila.getAttribute('data-color') || 'N/A',
                      tipo: fila.getAttribute('data-tipo') || 'N/A'
                    };
                    
                    self.log('Click en chat para: ' + vehiculoData.vehiculo);
                    self.abrirChatSofiEspecifico(vehiculoData);
                  }
                }
              });
              
              this.log('Chat Sofi configurado exitosamente');
            },
            
            abrirChatSofiEspecifico: function(vehiculoData) {
              this.log('=== ABRIENDO CHAT SOFI ESPECÍFICO ===');
              this.log('Vehículo: ' + vehiculoData.vehiculo);
              this.log('Precio: $' + vehiculoData.precio);
              this.log('Ubicación: ' + vehiculoData.ubicacion);
              
              var mensaje = this.generarMensajePersonalizado(vehiculoData);
              this.log('Mensaje generado: ' + mensaje.substring(0, 100) + '...');
              
              this.enviarMensajeATidio(mensaje);
            },
            
            generarMensajePersonalizado: function(data) {
              var precio = this.formatearPrecio(data.precio);
              var km = this.formatearKilometraje(data.km);
              
              var mensaje = 'Hola Sofi, me interesa obtener información sobre este vehículo ' + 
                           '(' + data.vehiculo + ', ' + data.año + ', ' + km + ', ' + 
                           data.transmision + ', ' + data.combustible + ', ' + data.color + '). ' +
                           'El precio publicado es $' + precio + ' MXN y se encuentra en ' + data.ubicacion + '. ' +
                           'Me gustaría conocer las opciones de financiamiento disponibles, incluyendo ' +
                           'un estimado de enganche mínimo requerido, plazos de pago y mensualidades aproximadas. ' +
                           'También quisiera agendar una prueba de manejo y saber si es posible evaluar mi auto actual para tomarlo a cuenta. ' +
                           '¿Podrías ayudarme con esta información? Gracias.';
              
              return mensaje;
            },
            
            formatearPrecio: function(precio) {
              var num = parseInt(precio) || 0;
              return num.toLocaleString('es-MX');
            },
            
            formatearKilometraje: function(km) {
              var num = parseInt(km) || 0;
              return num.toLocaleString('es-MX') + ' km';
            },
            
            enviarMensajeATidio: function(mensaje) {
              var self = this;
              
              this.log('Intentando enviar mensaje a Tidio...');
              
              // Función para enviar mensaje
              var enviarMensaje = function() {
                if (typeof tidioChatApi !== 'undefined') {
                  try {
                    self.log('Tidio API disponible, abriendo chat...');
                    tidioChatApi.open();
                    
                    setTimeout(function() {
                      try {
                        tidioChatApi.messageFromVisitor(mensaje);
                        self.log('Mensaje enviado exitosamente');
                      } catch (error) {
                        self.log('Error enviando mensaje: ' + error.message, 'error');
                      }
                    }, 1000);
                  } catch (error) {
                    self.log('Error abriendo Tidio: ' + error.message, 'error');
                  }
                } else {
                  self.log('Tidio API no disponible aún', 'warn');
                  return false;
                }
                return true;
              };
              
              // Intentar enviar inmediatamente
              if (!enviarMensaje()) {
                // Si no funciona, intentar después de 2 segundos
                this.log('Reintentando en 2 segundos...');
                setTimeout(function() {
                  if (!enviarMensaje()) {
                    // Último intento después de 5 segundos
                    self.log('Último intento en 5 segundos...');
                    setTimeout(function() {
                      if (!enviarMensaje()) {
                        self.log('No se pudo conectar con Tidio después de múltiples intentos', 'error');
                        alert('Error: No se pudo conectar con el chat. Por favor, recarga la página e intenta de nuevo.');
                      }
                    }, 5000);
                  }
                }, 2000);
              }
            },
            
            abrirChatSofiGeneral: function() {
              this.log('=== ABRIENDO CHAT SOFI GENERAL ===');
              
              var mensaje = 'Hola Sofi! 👋 Quiero explorar opciones de autos seminuevos.\n\n' +
                           '🔍 ME INTERESA:\n' +
                           '• Conocer el inventario disponible\n' +
                           '• Opciones de financiamiento\n' +
                           '• Evaluación de mi auto actual\n' +
                           '• Asesoría para encontrar el vehículo perfecto\n\n' +
                           '¿Puedes ayudarme a encontrar el auto ideal según mis necesidades y presupuesto? 🚗💡';
              
              this.enviarMensajeATidio(mensaje);
            },
            
            configurarEventos: function() {
              this.log('Configurando eventos...');
              var self = this;
              
              this.configurarBusqueda();
              this.configurarOrdenamientoCorregido();
              this.configurarLimpiarFiltros();
              this.configurarFiltrosHeader();
              this.configurarOpcionesFiltro();
              this.configurarCerrarDropdowns();
              this.configurarRemoverTags();
              
              this.log('Todos los eventos configurados');
            },
            
            configurarOrdenamientoCorregido: function() {
              this.log('=== CONFIGURANDO ORDENAMIENTO ===');
              var self = this;
              
              var botones = document.querySelectorAll('.sort-btn');
              this.log('Botones de ordenamiento encontrados: ' + botones.length);
              
              for (var i = 0; i &lt; botones.length; i++) {
                var boton = botones[i];
                var tipoOrden = boton.getAttribute('data-sort');
                
                (function(btn, tipo) {
                  btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    self.log('Click en ordenamiento: ' + tipo);
                    self.ejecutarOrdenamiento(tipo);
                  });
                })(boton, tipoOrden);
              }
              
              this.log('Ordenamiento configurado');
            },
            
            ejecutarOrdenamiento: function(tipo) {
              if (!tipo) {
                this.log('Tipo de ordenamiento no válido', 'error');
                return;
              }
              
              this.log('Ejecutando ordenamiento por: ' + tipo);
              this.ordenActual = tipo;
              
              this.actualizarBotonesOrdenamiento(tipo);
              this.aplicarOrdenamiento();
              this.renderizar();
            },
            
            actualizarBotonesOrdenamiento: function(tipoActivo) {
              var botones = document.querySelectorAll('.sort-btn');
              for (var i = 0; i &lt; botones.length; i++) {
                botones[i].classList.remove('active');
              }
              
              var botonActivo = document.querySelector('.sort-btn[data-sort="' + tipoActivo + '"]');
              if (botonActivo) {
                botonActivo.classList.add('active');
              }
            },
            
            aplicarOrdenamiento: function() {
              var self = this;
              
              if (this.ordenActual === 'precio') {
                this.vehiculosFiltrados.sort(function(a, b) {
                  return a.precio - b.precio;
                });
              } else if (this.ordenActual === 'km') {
                this.vehiculosFiltrados.sort(function(a, b) {
                  return a.km - b.km;
                });
              }
            },
            
            configurarBusqueda: function() {
              var input = document.getElementById('search-input');
              if (!input) {
                this.log('Input de búsqueda no encontrado', 'error');
                return;
              }
              
              var self = this;
              var timeout;
              
              input.addEventListener('input', function(e) {
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                  self.buscar(e.target.value);
                }, 300);
              });
              
              this.log('Búsqueda configurada');
            },
            
            configurarLimpiarFiltros: function() {
              var boton = document.getElementById('clear-filters-btn');
              if (!boton) {
                this.log('Botón limpiar no encontrado', 'error');
                return;
              }
              
              var self = this;
              boton.addEventListener('click', function() {
                self.log('Click en limpiar filtros');
                self.limpiarFiltros();
              });
              
              this.log('Limpiar filtros configurado');
            },
            
            configurarFiltrosHeader: function() {
              var headers = document.querySelectorAll('.filter-header');
              var self = this;
              
              this.log('Headers de filtro encontrados: ' + headers.length);
              
              for (var i = 0; i &lt; headers.length; i++) {
                headers[i].addEventListener('click', function(e) {
                  e.stopPropagation();
                  self.toggleDropdown(this);
                });
              }
              
              this.log('Headers de filtro configurados');
            },
            
            configurarOpcionesFiltro: function() {
              var opciones = document.querySelectorAll('.filter-option');
              var self = this;
              
              this.log('Opciones de filtro encontradas: ' + opciones.length);
              
              for (var i = 0; i &lt; opciones.length; i++) {
                opciones[i].addEventListener('click', function(e) {
                  e.stopPropagation();
                  var tipo = this.getAttribute('data-tipo');
                  var valor = this.getAttribute('data-valor');
                  self.log('Click en opción: ' + tipo + ' = ' + valor);
                  self.seleccionarOpcion(this);
                });
              }
              
              this.log('Opciones de filtro configuradas');
            },
            
            configurarCerrarDropdowns: function() {
              var self = this;
              document.addEventListener('click', function(e) {
                if (!e.target.closest('.filter-header') &amp;&amp; !e.target.closest('.filter-dropdown')) {
                  self.cerrarDropdowns();
                }
              });
              
              this.log('Cerrar dropdowns configurado');
            },
            
            configurarRemoverTags: function() {
              var self = this;
              document.addEventListener('click', function(e) {
                if (e.target.classList.contains('tag-remove')) {
                  self.log('Click en remover tag');
                  self.removerTag(e.target);
                }
              });
              
              this.log('Remover tags configurado');
            },
            
            buscar: function(termino) {
              this.log('Buscando: "' + termino + '"');
              
              if (!termino || termino.length === 0) {
                this.filtrosActivos.busqueda = '';
              } else {
                this.filtrosActivos.busqueda = termino.toLowerCase();
              }
              
              this.aplicarFiltros();
              this.renderizar();
              this.actualizarInterfaz();
            },
            
            toggleDropdown: function(header) {
              if (!header) return;
              
              var th = header.closest('th');
              if (!th) return;
              
              var dropdown = th.querySelector('.filter-dropdown');
              var arrow = header.querySelector('.filter-arrow');
              
              if (this.menuAbierto &amp;&amp; this.menuAbierto !== dropdown) {
                this.menuAbierto.classList.remove('show');
                var prevArrow = this.menuAbierto.parentNode.querySelector('.filter-arrow');
                if (prevArrow) prevArrow.classList.remove('rotated');
              }
              
              if (dropdown) {
                var isOpen = dropdown.classList.contains('show');
                dropdown.classList.toggle('show');
                if (arrow) arrow.classList.toggle('rotated');
                this.menuAbierto = isOpen ? null : dropdown;
              }
            },
            
            seleccionarOpcion: function(opcion) {
              if (!opcion) return;
              
              var tipo = opcion.getAttribute('data-tipo');
              var valor = opcion.getAttribute('data-valor');
              var etiqueta = opcion.textContent.replace('✓', '').trim();
              
              this.log('Seleccionando: ' + tipo + ' = ' + valor);
              
              var hermanos = opcion.parentNode.querySelectorAll('.filter-option');
              for (var i = 0; i &lt; hermanos.length; i++) {
                hermanos[i].classList.remove('selected');
              }
              
              if (this.filtrosActivos[tipo] === valor) {
                delete this.filtrosActivos[tipo];
                delete this.filtrosActivos[tipo + '_label'];
                this.log('Filtro removido: ' + tipo);
              } else {
                opcion.classList.add('selected');
                this.filtrosActivos[tipo] = valor;
                this.filtrosActivos[tipo + '_label'] = etiqueta;
                this.log('Filtro aplicado: ' + tipo + ' = ' + valor);
              }
              
              this.aplicarFiltros();
              this.renderizar();
              this.actualizarInterfaz();
              this.cerrarDropdowns();
            },
            
            aplicarFiltros: function() {
              var self = this;
              
              this.vehiculosFiltrados = this.vehiculos.filter(function(vehiculo) {
                if (self.filtrosActivos.busqueda) {
                  var termino = self.filtrosActivos.busqueda;
                  var coincide = vehiculo.vehiculo.toLowerCase().indexOf(termino) !== -1 ||
                                vehiculo.marca.toLowerCase().indexOf(termino) !== -1 ||
                                vehiculo.modelo.toLowerCase().indexOf(termino) !== -1 ||
                                vehiculo.año.toString().indexOf(termino) !== -1;
                  if (!coincide) return false;
                }
                
                if (self.filtrosActivos.marca &amp;&amp; vehiculo.marca !== self.filtrosActivos.marca) return false;
                if (self.filtrosActivos.precio &amp;&amp; self.obtenerRangoPrecio(vehiculo.precio) !== self.filtrosActivos.precio) return false;
                if (self.filtrosActivos.transmision &amp;&amp; vehiculo.transmision !== self.filtrosActivos.transmision) return false;
                if (self.filtrosActivos.combustible &amp;&amp; vehiculo.combustible !== self.filtrosActivos.combustible) return false;
                if (self.filtrosActivos.color &amp;&amp; vehiculo.color !== self.filtrosActivos.color) return false;
                if (self.filtrosActivos.tipo &amp;&amp; vehiculo.tipo !== self.filtrosActivos.tipo) return false;
                
                return true;
              });
              
              this.aplicarOrdenamiento();
              this.log('Filtros aplicados, resultados: ' + this.vehiculosFiltrados.length);
            },
            
            obtenerRangoPrecio: function(precio) {
              if (precio &lt; 300000) return 'economico';
              if (precio &lt; 500000) return 'accesible';
              if (precio &lt; 700000) return 'medio';
              if (precio &lt; 1000000) return 'premium';
              return 'lujo';
            },
            
            renderizar: function() {
              var tbody = document.querySelector('#tabla-inventario tbody');
              if (!tbody) {
                this.log('No se encontró tbody', 'error');
                return;
              }
              
              // Ocultar todos
              for (var i = 0; i &lt; this.vehiculos.length; i++) {
                this.vehiculos[i].elemento.classList.add('hidden');
              }
              
              // Mostrar y reordenar los filtrados
              for (var i = 0; i &lt; this.vehiculosFiltrados.length; i++) {
                var vehiculo = this.vehiculosFiltrados[i];
                vehiculo.elemento.classList.remove('hidden');
                tbody.appendChild(vehiculo.elemento);
              }
              
              this.log('Renderizado: ' + this.vehiculosFiltrados.length + ' vehículos visibles');
            },
            
            actualizarInterfaz: function() {
              this.actualizarContador();
              this.mostrarTagsFiltros();
              this.actualizarBotonLimpiar();
              this.actualizarHeadersFiltros();
            },
            
            actualizarContador: function() {
              var contador = document.getElementById('results-counter');
              if (contador) {
                contador.textContent = this.vehiculosFiltrados.length + ' vehículos encontrados';
              }
            },
            
            mostrarTagsFiltros: function() {
              var contenedor = document.getElementById('filter-tags');
              if (!contenedor) return;
              
              var html = '';
              var tipos = ['marca', 'precio', 'transmision', 'combustible', 'color', 'tipo'];
              
              for (var i = 0; i &lt; tipos.length; i++) {
                var tipo = tipos[i];
                if (this.filtrosActivos[tipo]) {
                  var etiqueta = this.filtrosActivos[tipo + '_label'] || this.filtrosActivos[tipo];
                  html += '&lt;div class="filter-tag" data-tipo="' + tipo + '"&gt;' + 
                         etiqueta + '&lt;span class="tag-remove"&gt;×&lt;/span&gt;&lt;/div&gt;';
                }
              }
              
              contenedor.innerHTML = html;
            },
            
            actualizarBotonLimpiar: function() {
              var boton = document.getElementById('clear-filters-btn');
              if (!boton) return;
              
              var hayFiltros = this.tieneFiltros();
              if (hayFiltros) {
                boton.classList.add('has-filters');
                boton.textContent = 'Limpiar Filtros';
              } else {
                boton.classList.remove('has-filters');
                boton.textContent = 'Sin Filtros';
              }
            },
            
            actualizarHeadersFiltros: function() {
              var tipos = ['marca', 'precio', 'transmision', 'combustible', 'color', 'tipo'];
              
              var headers = document.querySelectorAll('.table-header th');
              for (var i = 0; i &lt; headers.length; i++) {
                headers[i].classList.remove('has-active-filter');
              }
              
              for (var i = 0; i &lt; tipos.length; i++) {
                var tipo = tipos[i];
                if (this.filtrosActivos[tipo]) {
                  var th = document.querySelector('th[data-filter="' + tipo + '"]');
                  if (th) th.classList.add('has-active-filter');
                }
              }
            },
            
            tieneFiltros: function() {
              var tipos = ['marca', 'precio', 'transmision', 'combustible', 'color', 'tipo'];
              for (var i = 0; i &lt; tipos.length; i++) {
                if (this.filtrosActivos[tipos[i]]) return true;
              }
              return !!this.filtrosActivos.busqueda;
            },
            
            limpiarFiltros: function() {
              this.log('Limpiando todos los filtros');
              this.filtrosActivos = {};
              
              var input = document.getElementById('search-input');
              if (input) input.value = '';
              
              var opciones = document.querySelectorAll('.filter-option.selected');
              for (var i = 0; i &lt; opciones.length; i++) {
                opciones[i].classList.remove('selected');
              }
              
              this.aplicarFiltros();
              this.renderizar();
              this.actualizarInterfaz();
            },
            
            removerTag: function(elemento) {
              var tag = elemento.closest('.filter-tag');
              if (!tag) return;
              
              var tipo = tag.getAttribute('data-tipo');
              this.log('Removiendo tag: ' + tipo);
              
              delete this.filtrosActivos[tipo];
              delete this.filtrosActivos[tipo + '_label'];
              
              var opcion = document.querySelector('.filter-option[data-tipo="' + tipo + '"]');
              if (opcion) opcion.classList.remove('selected');
              
              this.aplicarFiltros();
              this.renderizar();
              this.actualizarInterfaz();
            },
            
            cerrarDropdowns: function() {
              if (this.menuAbierto) {
                this.menuAbierto.classList.remove('show');
                var arrow = this.menuAbierto.parentNode.querySelector('.filter-arrow');
                if (arrow) arrow.classList.remove('rotated');
                this.menuAbierto = null;
              }
            }
          };
          
          function inicializar() {
            if (document.readyState === 'loading') {
              document.addEventListener('DOMContentLoaded', function() {
                console.log('[INVENTARIO-SOFI] DOM Content Loaded - Inicializando...');
                InventarioSistema.init();
              });
            } else {
              console.log('[INVENTARIO-SOFI] DOM ya listo - Inicializando inmediatamente...');
              InventarioSistema.init();
            }
          }
          
          inicializar();
          
          window.InventarioSistema = InventarioSistema;
          
        })();
      </script>
    </t>

    <div id="wrap" class="oe_structure">
      <div class="dark-inventory">
        
        <!-- HEADER -->
        <header class="dark-header">
          <h1>INVENTARIO SEMINUEVOSMEX.NET</h1>
          <p>CDMX - PUEBLA - MEXICO | SISTEMA BIGDATA | CHAT SOFI INTEGRADO</p>
        </header>

        <!-- CONTROLES PRINCIPALES -->
        <div class="main-controls">
          
          <!-- BÚSQUEDA -->
          <div class="search-container">
            <input type="text" id="search-input" class="search-input" placeholder="Buscar vehículo (marca, modelo, año)..." autocomplete="off"/>
          </div>

          <!-- ORDENAMIENTO Y LIMPIAR -->
          <div class="control-row">
            <div class="sort-section">
              <span class="sort-label">Ordenar:</span>
              <div class="sort-btn active" data-sort="precio">▲ Precio</div>
              <div class="sort-btn" data-sort="km">▲ Kilómetros</div>
            </div>
            <div id="clear-filters-btn" class="clear-btn">Sin Filtros</div>
          </div>

          <!-- TAGS DE FILTROS -->
          <div class="filter-tags-container">
            <div id="filter-tags" class="filter-tags">
              <!-- Tags dinámicos -->
            </div>
          </div>

          <!-- CONTADOR -->
          <div class="results-counter">
            <span id="results-counter">10 vehículos encontrados</span>
          </div>

        </div>

        <!-- TABLA CON CHAT SOFI INTEGRADO -->
        <div class="table-container">
          <table id="tabla-inventario" class="inventory-table">
            <thead class="table-header">
              <tr>
                <th data-filter="marca">
                  <div class="filter-header">
                    VEHÍCULO <span class="filter-arrow">▼</span>
                  </div>
                  <div class="filter-dropdown">
                    <div class="filter-options">
                      <div class="filter-option" data-tipo="marca" data-valor="volkswagen">Volkswagen</div>
                      <div class="filter-option" data-tipo="marca" data-valor="bmw">BMW</div>
                      <div class="filter-option" data-tipo="marca" data-valor="toyota">Toyota</div>
                      <div class="filter-option" data-tipo="marca" data-valor="ford">Ford</div>
                      <div class="filter-option" data-tipo="marca" data-valor="honda">Honda</div>
                      <div class="filter-option" data-tipo="marca" data-valor="audi">Audi</div>
                      <div class="filter-option" data-tipo="marca" data-valor="nissan">Nissan</div>
                      <div class="filter-option" data-tipo="marca" data-valor="chevrolet">Chevrolet</div>
                      <div class="filter-option" data-tipo="marca" data-valor="mazda">Mazda</div>
                    </div>
                  </div>
                </th>
                <th data-filter="precio">
                  <div class="filter-header">
                    PRECIO <span class="filter-arrow">▼</span>
                  </div>
                  <div class="filter-dropdown">
                    <div class="filter-options">
                      <div class="filter-option" data-tipo="precio" data-valor="economico">Económico (&lt;300K)</div>
                      <div class="filter-option" data-tipo="precio" data-valor="accesible">Accesible (300-500K)</div>
                      <div class="filter-option" data-tipo="precio" data-valor="medio">Medio (500-700K)</div>
                      <div class="filter-option" data-tipo="precio" data-valor="premium">Premium (700K-1M)</div>
                      <div class="filter-option" data-tipo="precio" data-valor="lujo">Lujo (+1M)</div>
                    </div>
                  </div>
                </th>
                <th data-filter="detalles">
                  <div class="filter-header">
                    DETALLES <span class="filter-arrow">▼</span>
                  </div>
                  <div class="filter-dropdown">
                    <div class="filter-options">
                      <div class="filter-option" data-tipo="transmision" data-valor="automatica">Automática</div>
                      <div class="filter-option" data-tipo="transmision" data-valor="manual">Manual</div>
                      <div class="filter-option" data-tipo="transmision" data-valor="cvt">CVT</div>
                      <div class="filter-option" data-tipo="combustible" data-valor="gasolina">Gasolina</div>
                      <div class="filter-option" data-tipo="combustible" data-valor="premium">Premium</div>
                      <div class="filter-option" data-tipo="combustible" data-valor="hibrido">Híbrido</div>
                      <div class="filter-option" data-tipo="tipo" data-valor="sedan">Sedán</div>
                      <div class="filter-option" data-tipo="tipo" data-valor="suv">SUV</div>
                      <div class="filter-option" data-tipo="tipo" data-valor="hatchback">Hatchback</div>
                      <div class="filter-option" data-tipo="tipo" data-valor="pickup">Pickup</div>
                    </div>
                  </div>
                </th>
                <th data-filter="color">
                  <div class="filter-header">
                    COLOR <span class="filter-arrow">▼</span>
                  </div>
                  <div class="filter-dropdown">
                    <div class="filter-options">
                      <div class="filter-option" data-tipo="color" data-valor="azul">Azul</div>
                      <div class="filter-option" data-tipo="color" data-valor="gris">Gris</div>
                      <div class="filter-option" data-tipo="color" data-valor="blanco">Blanco</div>
                      <div class="filter-option" data-tipo="color" data-valor="negro">Negro</div>
                      <div class="filter-option" data-tipo="color" data-valor="rojo">Rojo</div>
                      <div class="filter-option" data-tipo="color" data-valor="plata">Plata</div>
                      <div class="filter-option" data-tipo="color" data-valor="amarillo">Amarillo</div>
                    </div>
                  </div>
                </th>
                <th>CHAT SOFI</th>
              </tr>
            </thead>
            <tbody class="table-body">
              
              <tr data-vehiculo="Volkswagen Virtus" data-marca="volkswagen" data-modelo="virtus" data-año="2024" data-precio="335900" data-km="4486" data-transmision="automatica" data-combustible="gasolina" data-color="azul" data-tipo="sedan" data-ubicacion="Puebla" data-variante="Trendline Aut">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Volkswagen Virtus</div>
                  <div class="vehicle-details">Trendline Aut • 2024 • Puebla</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$369,490</span>
                  <span class="price-current">$335,900</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">4,486km</span> <span class="spec-tag">automatica</span><br/>
                  <span class="spec-tag">gasolina</span> <span class="spec-tag">sedan</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-azul">azul</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="BMW X3" data-marca="bmw" data-modelo="x3" data-año="2023" data-precio="850000" data-km="6000" data-transmision="automatica" data-combustible="premium" data-color="gris" data-tipo="suv" data-ubicacion="Mexico" data-variante="xDrive30i">
                <td class="vehicle-cell">
                  <div class="vehicle-name">BMW X3</div>
                  <div class="vehicle-details">xDrive30i • 2023 • Mexico</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$900,000</span>
                  <span class="price-current">$850,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">6,000km</span> <span class="spec-tag">automatica</span><br/>
                  <span class="spec-tag">premium</span> <span class="spec-tag">suv</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-gris">gris</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Toyota Prius" data-marca="toyota" data-modelo="prius" data-año="2022" data-precio="480000" data-km="8500" data-transmision="automatica" data-combustible="hibrido" data-color="blanco" data-tipo="hatchback" data-ubicacion="Veracruz" data-variante="Hybrid">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Toyota Prius</div>
                  <div class="vehicle-details">Hybrid • 2022 • Veracruz</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$520,000</span>
                  <span class="price-current">$480,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">8,500km</span> <span class="spec-tag">automatica</span><br/>
                  <span class="spec-tag">hibrido</span> <span class="spec-tag">hatchback</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-blanco">blanco</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Ford F-150" data-marca="ford" data-modelo="f-150" data-año="2021" data-precio="620000" data-km="15000" data-transmision="automatica" data-combustible="gasolina" data-color="negro" data-tipo="pickup" data-ubicacion="Puebla" data-variante="XLT Crew Cab">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Ford F-150</div>
                  <div class="vehicle-details">XLT Crew Cab • 2021 • Puebla</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$680,000</span>
                  <span class="price-current">$620,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">15,000km</span> <span class="spec-tag">automatica</span><br/>
                  <span class="spec-tag">gasolina</span> <span class="spec-tag">pickup</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-negro">negro</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Honda Civic" data-marca="honda" data-modelo="civic" data-año="2020" data-precio="285000" data-km="25000" data-transmision="manual" data-combustible="gasolina" data-color="rojo" data-tipo="sedan" data-ubicacion="Tlaxcala" data-variante="Sport Manual">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Honda Civic</div>
                  <div class="vehicle-details">Sport Manual • 2020 • Tlaxcala</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$320,000</span>
                  <span class="price-current">$285,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">25,000km</span> <span class="spec-tag">manual</span><br/>
                  <span class="spec-tag">gasolina</span> <span class="spec-tag">sedan</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-rojo">rojo</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Audi Q7" data-marca="audi" data-modelo="q7" data-año="2023" data-precio="1200000" data-km="3500" data-transmision="automatica" data-combustible="premium" data-color="negro" data-tipo="suv" data-ubicacion="Mexico" data-variante="Premium Plus">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Audi Q7</div>
                  <div class="vehicle-details">Premium Plus • 2023 • Mexico</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$1,350,000</span>
                  <span class="price-current">$1,200,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">3,500km</span> <span class="spec-tag">automatica</span><br/>
                  <span class="spec-tag">premium</span> <span class="spec-tag">suv</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-negro">negro</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Nissan Sentra" data-marca="nissan" data-modelo="sentra" data-año="2022" data-precio="310000" data-km="18000" data-transmision="cvt" data-combustible="gasolina" data-color="plata" data-tipo="sedan" data-ubicacion="Veracruz" data-variante="Advance CVT">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Nissan Sentra</div>
                  <div class="vehicle-details">Advance CVT • 2022 • Veracruz</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$340,000</span>
                  <span class="price-current">$310,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">18,000km</span> <span class="spec-tag">cvt</span><br/>
                  <span class="spec-tag">gasolina</span> <span class="spec-tag">sedan</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-plata">plata</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Chevrolet Tahoe" data-marca="chevrolet" data-modelo="tahoe" data-año="2021" data-precio="890000" data-km="22000" data-transmision="automatica" data-combustible="gasolina" data-color="blanco" data-tipo="suv" data-ubicacion="Puebla" data-variante="LT V8">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Chevrolet Tahoe</div>
                  <div class="vehicle-details">LT V8 • 2021 • Puebla</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$950,000</span>
                  <span class="price-current">$890,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">22,000km</span> <span class="spec-tag">automatica</span><br/>
                  <span class="spec-tag">gasolina</span> <span class="spec-tag">suv</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-blanco">blanco</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Mazda CX-5" data-marca="mazda" data-modelo="cx-5" data-año="2020" data-precio="420000" data-km="28000" data-transmision="automatica" data-combustible="gasolina" data-color="rojo" data-tipo="suv" data-ubicacion="Mexico" data-variante="Grand Touring">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Mazda CX-5</div>
                  <div class="vehicle-details">Grand Touring • 2020 • Mexico</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$460,000</span>
                  <span class="price-current">$420,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">28,000km</span> <span class="spec-tag">automatica</span><br/>
                  <span class="spec-tag">gasolina</span> <span class="spec-tag">suv</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-rojo">rojo</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
              <tr data-vehiculo="Volkswagen Golf" data-marca="volkswagen" data-modelo="golf" data-año="2019" data-precio="380000" data-km="35000" data-transmision="manual" data-combustible="premium" data-color="amarillo" data-tipo="hatchback" data-ubicacion="Tlaxcala" data-variante="GTI Manual">
                <td class="vehicle-cell">
                  <div class="vehicle-name">Volkswagen Golf</div>
                  <div class="vehicle-details">GTI Manual • 2019 • Tlaxcala</div>
                </td>
                <td class="price-cell">
                  <span class="price-old">$420,000</span>
                  <span class="price-current">$380,000</span>
                </td>
                <td class="details-cell">
                  <span class="spec-tag">35,000km</span> <span class="spec-tag">manual</span><br/>
                  <span class="spec-tag">premium</span> <span class="spec-tag">hatchback</span>
                </td>
                <td class="color-cell">
                  <span class="color-badge color-amarillo">amarillo</span>
                </td>
                <td class="chat-cell">
                  <button class="chat-btn">💬 Sofi</button>
                </td>
              </tr>
              
            </tbody>
          </table>
        </div>

      </div>
    </div>

  </t>
</t>