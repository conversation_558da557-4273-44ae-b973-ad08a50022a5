## 🚨 **REGLAS CRÍTICAS - NUNCA IGNORAR**

## **1. COMENTARIOS PROHIBIDOS**

`xml❌ INCORRECTO:
// Este es un comentario
/* Comentario de bloque */

✅ CORRECTO:
*<!-- Solo comentarios HTML -->*`

## **2. OPERADORES LÓGICOS - ESCAPAR SIEMPRE**

`xml❌ INCORRECTO:
if (a && b) return true;
if (price < 1000) return false;
if (items > 5) continue;

✅ CORRECTO:
if (a &amp;&amp; b) return true;
if (price &lt; 1000) return false;
if (items &gt; 5) continue;`

## **3. ESTRUCTURA XML OBLIGATORIA**

`xml✅ ESTRUCTURA CORRECTA:
<t t-name="website.tu-template">
  <t t-call="website.layout">
    <t t-set="head">
      <title>Tu Título</title>
      <meta name="description" content="Descripción"/>
      <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
      
      <script>
        // Tu JavaScript aquí
      </script>
      
      <style>
        /* Tu CSS aquí */
      </style>
    </t>
    
    <div class="tu-contenido">
      *<!-- Tu HTML aquí -->*
    </div>
  </t>
</t>`


# 📋 **SÍMBOLOS Y CARACTERES ESPECIALES - REGLAS INMUTABLES DE ODOO**

## 🚨 **CARACTERES QUE DEBEN ESCAPARSE SIEMPRE**

### **A. OPERADORES LÓGICOS (CRÍTICO)**
```xml
❌ INCORRECTO:
if (a && b) return true;
if (price  5) break;
if (a || b) return false;

✅ CORRECTO:
if (a &amp;&amp; b) return true;
if (price &lt; 100) continue;
if (count &gt; 5) break;
if (a || b) return false;
```

### **B. SÍMBOLOS MATEMÁTICOS**
```xml
❌ INCORRECTO:
var percent = 50%;
var discount = price * 0.15;
if (value >= 1000) return true;
if (amount content";
var query = "SELECT * FROM table WHERE id > 5";

✅ CORRECTO:
var message = "Precio &lt; $1000 &amp; disponible";
var html = "&lt;div&gt;content&lt;/div&gt;";
var query = "SELECT * FROM table WHERE id &gt; 5";
```

## 📝 **TABLA COMPLETA DE ESCAPE DE CARACTERES**

| **Carácter** | **Uso Incorrecto** | **Escape Correcto** | **Contexto** |
|--------------|-------------------|-------------------|--------------|
| `&` | `a & b` | `a &amp; b` | Operador AND lógico |
| `&&` | `a && b` | `a &amp;&amp; b` | Operador AND lógico doble |
| `` | `if (a > b)` | `if (a &gt; b)` | Comparación mayor que |
| `=` | `if (a >= b)` | `if (a &gt;= b)` | Mayor o igual que |
| `"` | `var x = "text"` | `var x = 'text'` | Usar comillas simples |
| `%` | `width: 100%` | `width: 100%` | ✅ OK en CSS |
| `%` | `var x = 50%` | `var x = 50` | ❌ Quitar en JS |

## 🔧 **NORMAS INMUTABLES ESPECÍFICAS**

### **A. JAVASCRIPT - CARACTERES PROHIBIDOS**
```javascript
❌ NUNCA USAR EN ODOO:
// Comentarios de línea
/* Comentarios de bloque */
${template} literals
`backticks`
@decorators
#private fields

✅ SIEMPRE USAR:

'string concatenation' + variable
"double quotes solo cuando necesario"
```

### **B. ATRIBUTOS HTML - ESCAPE OBLIGATORIO**
```xml
❌ INCORRECTO:


 5">

✅ CORRECTO:



```

### **C. CSS - CARACTERES ESPECIALES**
```css
✅ PERMITIDOS EN CSS:
width: 100%;
height: calc(100% - 50px);
content: "";
content: "&";

❌ CUIDADO EN CSS-IN-JS:
/* Si CSS está dentro de  en XML, escapar */
```

## 🎯 **CASOS ESPECÍFICOS CRÍTICOS**

### **A. Condiciones de Filtro**
```javascript
❌ CÓDIGO PROBLEMÁTICO:
if (price >= 200000 && price  minPrice && array[i].available) {
    // lógica
  }
}

✅ CORRECTO:
for (var i = 0; i &lt; array.length; i++) {
  if (array[i].price &gt; minPrice &amp;&amp; array[i].available) {
    /* lógica */
  }
}
```

### **C. Event Handlers**
```xml
❌ INCORRECTO:
 0 && isValid) doSomething()">

✅ CORRECTO:

```

## 🔍 **DETECCIÓN DE ERRORES COMUNES**

### **A. Búsqueda de Patterns Problemáticos**
```javascript
/* BUSCAR Y REEMPLAZAR ESTOS PATTERNS: */

/* Pattern 1: Operadores lógicos */
Buscar: &&
Reemplazar: &amp;&amp;

/* Pattern 2: Comparaciones */
Buscar: 
Reemplazar: &gt;

/* Pattern 3: Ampersands */
Buscar: &
Reemplazar: &amp;
```

### **B. Validación de Sintaxis XML**
```xml
✅ ESTRUCTURA VÁLIDA:

  if (condition &amp;&amp; otherCondition) {
    return value &gt; threshold &amp;&amp; value &lt; maximum;
  }


❌ ESTRUCTURA INVÁLIDA:

  if (condition && otherCondition) {  // Causará error XML
    return value > threshold && value 
```

## 📊 **CARACTERES EN DIFERENTES CONTEXTOS**

### **A. En Strings JavaScript**
```javascript
✅ CORRECTO:
var message = 'El precio es mayor que $1000';
var condition = 'Si el valor es menor o igual que 500';
var logic = 'Usar AND lógico para combinar condiciones';

❌ EVITAR:
var message = 'El precio es > $1000';  // Problemático en XML
var condition = 'Si valor 



❌ PROBLEMÁTICO:
1000">


```

### **C. En Expresiones Regulares**
```javascript
✅ SEGURO:
var regex = new RegExp('pattern');
var match = text.indexOf('search') !== -1;

❌ PROBLEMÁTICO EN XML:
var regex = /pattern>/g;  // El > puede causar problemas
```

## 🛠️ **HERRAMIENTAS DE VALIDACIÓN**

### **A. Checklist Pre-Deploy**
```bash
✅ VERIFICAR ANTES DE SUBIR:
□ Sin && sin escapar
□ Sin  sin escapar  
□ Sin comentarios //
□ Sin template literals ``
□ Sin % en JavaScript
□ Comillas simples preferidas
□ Atributos data sin caracteres especiales
```

### **B. Regex para Buscar Problemas**
```javascript
/* USAR ESTOS REGEX PARA ENCONTRAR PROBLEMAS: */

/* Operadores sin escapar */
/&&(?!amp;)/g
/(?![^
  // Comentario
  if (a && b > c) {
    /* comentario */
  }


❌ FATAL - XML MALFORMADO:
 5 && param 

❌ FATAL - TEMPLATE LITERALS:

  var html = `${content}`;

```

### **B. Síntomas de Errores:**
- Página en blanco
- Error 500 Internal Server Error
- "Template not found"
- XML parsing errors
- JavaScript execution failed

## ✅ **TEMPLATE SEGURO COMPLETO**

```xml

  
    
      
        function safeFunction() {
          var condition = true;
          if (value &gt; 100 &amp;&amp; condition) {
            return 'valid';
          }
          
          for (var i = 0; i &lt; items.length; i++) {
            if (items[i].price &gt;= minPrice &amp;&amp; items[i].available) {
              /* procesamiento seguro */
            }
          }
        }
      
      
      
        .class {
          width: 100%;
          height: calc(100% - 20px);
        }
      
    
    
    
      
        Botón Seguro
      
    
  

```

## 🎯 **RESUMEN DE SÍMBOLOS CRÍTICOS**

| **Símbolo** | **Contexto** | **Escape** | **Obligatorio** |
|-------------|--------------|------------|-----------------|
| `&&` | JavaScript | `&amp;&amp;` | ✅ SÍ |
| `` | Comparaciones | `&gt;` | ✅ SÍ |
| `&` | Texto/Logic | `&amp;` | ✅ SÍ |
| `%` | JavaScript | Quitar símbolo | ✅ SÍ |
| `%` | CSS | Sin escape | ✅ OK |
| `"` | Strings | Usar `'` | 🟡 Recomendado |
| `//` | Comentarios | `` | ✅ SÍ |

**🚨 REGLA DE ORO:** Cuando dudes, escapa siempre. Es mejor prevenir que debuggear errores XML en producción.
